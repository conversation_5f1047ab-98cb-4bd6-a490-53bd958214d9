* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    background-color: #f5f5f5;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* File options styles */
.file-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.file-options .option {
    padding: 15px;
    background-color: #fff;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.file-options label {
    color: #444;
}

.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-start;
}

/* Full width container for cards view */
.container.full-width {
    max-width: none;
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
}

h1 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

/* Tabs */
.tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.tab-btn {
    padding: 10px 20px;
    margin: 0 5px;
    background-color: #f1f1f1;
    border: 1px solid #ddd;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
}

.tab-btn:hover {
    background-color: #e9e9e9;
}

.tab-btn.active {
    background-color: #4CAF50;
    color: white;
    border-color: #4CAF50;
}

.tab-content {
    margin-bottom: 20px;
}

.upload-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.parts-selector {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
}

input[type="file"] {
    display: block;
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

input[type="number"] {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    padding: 10px 15px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #45a049;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.result-section {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.stats {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #e9f7ef;
    border-radius: 4px;
}

pre {
    background-color: #f1f1f1;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.download-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
}

#download-btn {
    margin-bottom: 10px;
}

#split-download-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.split-download-btn {
    margin: 5px;
}

.error {
    color: #d9534f;
    background-color: #f9eaea;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
}

/* Progress Bar Styles */
.progress-container {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: bold;
    color: #495057;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-details {
    text-align: center;
    color: #6c757d;
    font-size: 14px;
}

/* Movie Cards Styles */
.cards-controls {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.search-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
}

.movies-count {
    font-weight: bold;
    color: #495057;
    background-color: #e9ecef;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 14px;
}

.cards-container {
    display: block; /* Changed from grid to block */
    margin-top: 20px;
    padding: 20px;
}

/* Full width cards container */
.container.full-width .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    padding: 20px 30px;
}

.movie-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e0e0e0;
}

.movie-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.movie-poster {
    width: 100%;
    height: 400px;
    object-fit: cover;
    background-color: #f0f0f0;
}

.movie-poster.error {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: #6c757d;
    font-size: 14px;
    text-align: center;
}

.movie-info {
    padding: 15px;
}

.movie-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
    line-height: 1.4;
}

.movie-actions {
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

.action-btn {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.play-btn {
    background-color: #28a745;
    color: white;
}

.play-btn:hover {
    background-color: #218838;
}

.edit-btn {
    background-color: #007bff;
    color: white;
}

.edit-btn:hover {
    background-color: #0056b3;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
}

.delete-btn:hover {
    background-color: #c82333;
}

/* Edit Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 15px;
}

.close:hover {
    color: #000;
}

.modal h3 {
    margin-bottom: 20px;
    color: #333;
}

.modal label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.modal input {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.modal-actions button {
    padding: 10px 20px;
}

/* Clean tab styles */
.clean-input-section {
    margin: 20px 0;
}

.clean-input-section label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.clean-buttons {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.clean-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: background-color 0.3s;
}

#clean-btn {
    background-color: #dc3545;
    color: white;
}

#clean-btn:hover {
    background-color: #c82333;
}

#remove-duplicates-btn {
    background-color: #17a2b8;
    color: white;
}

#remove-duplicates-btn:hover {
    background-color: #138496;
}

.clean-preview {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.clean-preview h3 {
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.clean-stats {
    background-color: #e9ecef;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-weight: bold;
}

.removed-movies {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 10px;
}

.removed-movie-item {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
}

.removed-movie-item h4 {
    color: #721c24;
    margin-bottom: 5px;
}

.removed-movie-item p {
    color: #721c24;
    margin: 2px 0;
    font-size: 14px;
}

/* Folder Structure Styles */
.folder-breadcrumb {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.breadcrumb-item {
    color: #0066cc;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.breadcrumb-item:hover {
    background-color: #e9ecef;
}

.folders-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

.folder-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.folder-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.folder-icon {
    font-size: 2em;
    margin-left: 15px;
    color: #ffd700;
}

.folder-info {
    flex-grow: 1;
}

.folder-name {
    font-weight: bold;
    font-size: 1.1em;
    color: #333;
    margin-bottom: 5px;
}

.folder-count {
    font-size: 0.9em;
    color: #666;
}

.movie-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cards-container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .movie-poster {
        height: 300px;
    }

    .movie-actions {
        flex-direction: column;
    }

    .action-btn {
        margin-bottom: 5px;
    }
    
    .folders-container {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
        padding: 15px;
    }
}
