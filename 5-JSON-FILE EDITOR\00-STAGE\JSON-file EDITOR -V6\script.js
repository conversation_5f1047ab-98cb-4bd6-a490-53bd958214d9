document.addEventListener('DOMContentLoaded', () => {
    // Get DOM elements
    const fileInput = document.getElementById('json-files');
    const combineBtn = document.getElementById('combine-btn');
    const downloadBtn = document.getElementById('download-btn');
    const previewElement = document.getElementById('preview');
    const statsElement = document.getElementById('stats');
    const resultSection = document.getElementById('result-section');
    const splitFileInput = document.getElementById('json-file-to-split');
    const partsCountInput = document.getElementById('parts-count');
    const splitBtn = document.getElementById('split-btn');
    const splitDownloadContainer = document.getElementById('split-download-container');
    const combineTabBtn = document.getElementById('combine-tab-btn');
    const splitTabBtn = document.getElementById('split-tab-btn');
    const analyzeTabBtn = document.getElementById('analyze-tab-btn');
    const cardsTabBtn = document.getElementById('cards-tab-btn');
    const cleanTabBtn = document.getElementById('clean-tab-btn');
    const combineTab = document.getElementById('combine-tab');
    const splitTab = document.getElementById('split-tab');
    const analyzeTab = document.getElementById('analyze-tab');
    const cardsTab = document.getElementById('cards-tab');
    const cleanTab = document.getElementById('clean-tab');
    const analyzeFileInput = document.getElementById('json-file-to-analyze');
    const analyzeBtn = document.getElementById('analyze-btn');
    const cardsFileInput = document.getElementById('json-file-for-cards');
    const cardsFolderInput = document.getElementById('json-folder-for-cards');
    const loadCardsBtn = document.getElementById('load-cards-btn');
    const cardsContainer = document.getElementById('cards-container');
    const downloadUpdatedBtn = document.getElementById('download-updated-btn');
    const cardsControls = document.getElementById('cards-controls');
    const searchInput = document.getElementById('search-input');
    const moviesCount = document.getElementById('movies-count');
    const editModal = document.getElementById('editModal');
    const editForm = document.getElementById('editForm');
    const editMovieName = document.getElementById('editMovieName');
    const editMovieImg = document.getElementById('editMovieImg');
    const editMovieHref = document.getElementById('editMovieHref');
    const cancelEditBtn = document.getElementById('cancelEdit');
    const closeModalBtn = document.querySelector('.close');

    // Clean tab elements
    const cleanFileInput = document.getElementById('json-file-to-clean');
    const movieNameInput = document.getElementById('movie-name-to-remove');
    const cleanBtn = document.getElementById('clean-btn');
    const removeDuplicatesBtn = document.getElementById('remove-duplicates-btn');
    const cleanPreview = document.getElementById('clean-preview');
    const cleanStats = document.getElementById('clean-stats');
    const removedMovies = document.getElementById('removed-movies');

    // Store data
    let combinedData = null;
    let splitData = null;
    let splitParts = [];
    let analyzedData = null;
    let cardsData = null;
    let currentEditIndex = -1;
    let allMovies = []; // Store all movies for search functionality
    let cleanedData = null;

    // Add event listeners
    combineBtn.addEventListener('click', combineJsonFiles);
    downloadBtn.addEventListener('click', downloadCombinedJson);
    splitBtn.addEventListener('click', splitJsonFile);
    analyzeBtn.addEventListener('click', analyzeJsonFile);
    loadCardsBtn.addEventListener('click', loadMovieCards);
    downloadUpdatedBtn.addEventListener('click', downloadUpdatedData);
    cleanBtn.addEventListener('click', cleanJsonFile);
    removeDuplicatesBtn.addEventListener('click', removeDuplicateMovies);

    // Modal event listeners
    closeModalBtn.addEventListener('click', closeEditModal);
    cancelEditBtn.addEventListener('click', closeEditModal);
    editForm.addEventListener('submit', saveMovieEdit);

    // Close modal when clicking outside
    window.addEventListener('click', (event) => {
        if (event.target === editModal) {
            closeEditModal();
        }
    });

    // Search functionality
    searchInput.addEventListener('input', (event) => {
        const searchTerm = event.target.value.toLowerCase().trim();
        filterMovies(searchTerm);
    });

    // Tab switching
    combineTabBtn.addEventListener('click', () => {
        combineTabBtn.classList.add('active');
        splitTabBtn.classList.remove('active');
        analyzeTabBtn.classList.remove('active');
        cardsTabBtn.classList.remove('active');
        cleanTabBtn.classList.remove('active');
        combineTab.style.display = 'block';
        splitTab.style.display = 'none';
        analyzeTab.style.display = 'none';
        cardsTab.style.display = 'none';
        cleanTab.style.display = 'none';

        // Reset UI for combine mode
        resetUI('combine');
    });

    splitTabBtn.addEventListener('click', () => {
        splitTabBtn.classList.add('active');
        combineTabBtn.classList.remove('active');
        analyzeTabBtn.classList.remove('active');
        cardsTabBtn.classList.remove('active');
        cleanTabBtn.classList.remove('active');
        splitTab.style.display = 'block';
        combineTab.style.display = 'none';
        analyzeTab.style.display = 'none';
        cardsTab.style.display = 'none';
        cleanTab.style.display = 'none';

        // Reset UI for split mode
        resetUI('split');
    });

    analyzeTabBtn.addEventListener('click', () => {
        analyzeTabBtn.classList.add('active');
        combineTabBtn.classList.remove('active');
        splitTabBtn.classList.remove('active');
        cardsTabBtn.classList.remove('active');
        cleanTabBtn.classList.remove('active');
        analyzeTab.style.display = 'block';
        combineTab.style.display = 'none';
        splitTab.style.display = 'none';
        cardsTab.style.display = 'none';
        cleanTab.style.display = 'none';

        // Reset UI for analyze mode
        resetUI('analyze');
    });

    cardsTabBtn.addEventListener('click', () => {
        cardsTabBtn.classList.add('active');
        combineTabBtn.classList.remove('active');
        splitTabBtn.classList.remove('active');
        analyzeTabBtn.classList.remove('active');
        cleanTabBtn.classList.remove('active');
        cardsTab.style.display = 'block';
        combineTab.style.display = 'none';
        splitTab.style.display = 'none';
        analyzeTab.style.display = 'none';
        cleanTab.style.display = 'none';

        // Reset UI for cards mode
        resetUI('cards');
    });

    cleanTabBtn.addEventListener('click', () => {
        cleanTabBtn.classList.add('active');
        combineTabBtn.classList.remove('active');
        splitTabBtn.classList.remove('active');
        analyzeTabBtn.classList.remove('active');
        cardsTabBtn.classList.remove('active');
        cleanTab.style.display = 'block';
        combineTab.style.display = 'none';
        splitTab.style.display = 'none';
        analyzeTab.style.display = 'none';
        cardsTab.style.display = 'none';

        // Reset UI for clean mode
        resetUI('clean');
    });

    // Function to reset UI based on mode
    function resetUI(mode) {
        previewElement.textContent = '';
        statsElement.textContent = '';

        if (mode === 'combine') {
            downloadBtn.disabled = true;
            downloadBtn.style.display = 'block';
            downloadBtn.textContent = 'تحميل الملف المدمج';
            downloadBtn.onclick = downloadCombinedJson;
            splitDownloadContainer.style.display = 'none';
        } else if (mode === 'split') {
            downloadBtn.style.display = 'none';
            splitDownloadContainer.style.display = 'none';
            splitDownloadContainer.innerHTML = '';
        } else if (mode === 'analyze') {
            downloadBtn.style.display = 'none';
            splitDownloadContainer.style.display = 'none';
            splitDownloadContainer.innerHTML = '';
        } else if (mode === 'cards') {
            downloadBtn.style.display = 'none';
            splitDownloadContainer.style.display = 'none';
            splitDownloadContainer.innerHTML = '';
            cardsContainer.innerHTML = '';
            downloadUpdatedBtn.style.display = 'none';
            cardsControls.style.display = 'none';
            searchInput.value = '';
        } else if (mode === 'clean') {
            downloadBtn.disabled = true;
            downloadBtn.style.display = 'block';
            downloadBtn.textContent = 'تحميل الملف المنظف';
            downloadBtn.onclick = downloadCleanedJson;
            splitDownloadContainer.style.display = 'none';
            cleanPreview.style.display = 'none';
            movieNameInput.value = '';
        }
    }

    // Function to combine JSON files
    async function combineJsonFiles() {
        const files = fileInput.files;

        // Check if files are selected
        if (files.length === 0) {
            showError('الرجاء اختيار ملف واحد على الأقل.');
            return;
        }

        try {
            // Reset UI
            previewElement.textContent = '';
            statsElement.textContent = '';
            downloadBtn.disabled = true;
            splitDownloadContainer.style.display = 'none';

            // Initialize combined data
            combinedData = {
                movies_info: []
            };

            // Process each file
            let totalMovies = 0;
            let processedFiles = 0;

            for (const file of files) {
                const fileContent = await readFile(file);

                try {
                    // Parse JSON
                    const jsonData = JSON.parse(fileContent);

                    // Validate structure
                    if (!jsonData.movies_info || !Array.isArray(jsonData.movies_info)) {
                        showError(`الملف ${file.name} لا يحتوي على بنية صحيحة. يجب أن يحتوي على مصفوفة "movies_info".`);
                        continue;
                    }

                    // Add movies to combined data
                    combinedData.movies_info = combinedData.movies_info.concat(jsonData.movies_info);

                    // Update stats
                    totalMovies += jsonData.movies_info.length;
                    processedFiles++;

                } catch (error) {
                    showError(`خطأ في معالجة الملف ${file.name}: ${error.message}`);
                }
            }

            // Display results
            if (processedFiles > 0) {
                statsElement.innerHTML = `
                    <p>تم معالجة <strong>${processedFiles}</strong> ملف.</p>
                    <p>إجمالي الأفلام: <strong>${totalMovies}</strong></p>
                `;

                // Show preview (limited to first 5 items)
                const previewData = {
                    movies_info: combinedData.movies_info.slice(0, 5)
                };

                if (combinedData.movies_info.length > 5) {
                    previewData._note = `عرض 5 من أصل ${combinedData.movies_info.length} فيلم`;
                }

                previewElement.textContent = JSON.stringify(previewData, null, 2);

                // Enable download button
                downloadBtn.disabled = false;
                downloadBtn.style.display = 'block';
            } else {
                showError('لم يتم معالجة أي ملفات بنجاح.');
            }

        } catch (error) {
            showError(`حدث خطأ: ${error.message}`);
        }
    }

    // Function to split JSON file
    async function splitJsonFile() {
        const file = splitFileInput.files[0];
        const partsCount = parseInt(partsCountInput.value);

        // Validate inputs
        if (!file) {
            showError('الرجاء اختيار ملف JSON للتجزئة.');
            return;
        }

        if (isNaN(partsCount) || partsCount < 1 || partsCount > 10) {
            showError('عدد الأجزاء يجب أن يكون بين 1 و 10.');
            return;
        }

        try {
            // Reset UI
            previewElement.textContent = '';
            statsElement.textContent = '';
            downloadBtn.style.display = 'none';
            splitDownloadContainer.style.display = 'none';
            splitDownloadContainer.innerHTML = '';

            // Read and parse the file
            const fileContent = await readFile(file);
            splitData = JSON.parse(fileContent);

            // Validate structure
            if (!splitData.movies_info || !Array.isArray(splitData.movies_info)) {
                showError(`الملف ${file.name} لا يحتوي على بنية صحيحة. يجب أن يحتوي على مصفوفة "movies_info".`);
                return;
            }

            const totalMovies = splitData.movies_info.length;

            // Check if splitting is needed
            if (partsCount === 1) {
                showError('تم اختيار جزء واحد فقط. لا حاجة للتجزئة.');
                return;
            }

            if (totalMovies < partsCount) {
                showError(`عدد الأفلام (${totalMovies}) أقل من عدد الأجزاء المطلوبة (${partsCount}). يرجى اختيار عدد أجزاء أقل.`);
                return;
            }

            // Calculate items per part
            const itemsPerPart = Math.ceil(totalMovies / partsCount);

            // Create split parts
            splitParts = [];

            for (let i = 0; i < partsCount; i++) {
                const startIndex = i * itemsPerPart;
                const endIndex = Math.min(startIndex + itemsPerPart, totalMovies);

                if (startIndex >= totalMovies) break;

                const partData = {
                    movies_info: splitData.movies_info.slice(startIndex, endIndex)
                };

                splitParts.push(partData);
            }

            // Display stats
            statsElement.innerHTML = `
                <p>تم تجزئة الملف <strong>${file.name}</strong> إلى <strong>${splitParts.length}</strong> أجزاء.</p>
                <p>إجمالي الأفلام: <strong>${totalMovies}</strong></p>
                <p>عدد الأفلام في كل جزء: <strong>${itemsPerPart}</strong> (تقريباً)</p>
            `;

            // Show preview of first part
            const previewData = {
                part: 1,
                total_parts: splitParts.length,
                movies_count: splitParts[0].movies_info.length,
                movies_info: splitParts[0].movies_info.slice(0, 5)
            };

            if (splitParts[0].movies_info.length > 5) {
                previewData._note = `عرض 5 من أصل ${splitParts[0].movies_info.length} فيلم في الجزء الأول`;
            }

            previewElement.textContent = JSON.stringify(previewData, null, 2);

            // Create download buttons for each part
            createSplitDownloadButtons(file.name, splitParts);

        } catch (error) {
            showError(`حدث خطأ: ${error.message}`);
        }
    }

    // Function to create download buttons for split parts
    function createSplitDownloadButtons(originalFileName, parts) {
        splitDownloadContainer.innerHTML = '';

        // Extract base name from original file
        let baseName = originalFileName.replace('.json', '');

        // Try to extract domain from the first movie's URL if available
        if (parts[0].movies_info.length > 0 && parts[0].movies_info[0].movies_href) {
            try {
                const url = new URL(parts[0].movies_info[0].movies_href);
                let domain = url.hostname.replace('www.', '');

                // If hostname has multiple parts, take the domain name part
                const domainParts = domain.split('.');
                if (domainParts.length >= 2) {
                    baseName = domainParts[domainParts.length - 2];
                } else {
                    baseName = domain;
                }
            } catch (e) {
                // If URL parsing fails, try to extract domain using regex
                const match = parts[0].movies_info[0].movies_href.match(/\/\/(?:www\.)?([^\/]+)/i);
                if (match && match[1]) {
                    baseName = match[1].split('.')[0];
                }
            }
        }

        // Create container title
        const containerTitle = document.createElement('h3');
        containerTitle.textContent = 'تحميل الأجزاء:';
        containerTitle.style.marginBottom = '10px';
        containerTitle.style.textAlign = 'center';
        splitDownloadContainer.appendChild(containerTitle);

        // Create buttons container
        const buttonsDiv = document.createElement('div');
        buttonsDiv.style.display = 'flex';
        buttonsDiv.style.flexWrap = 'wrap';
        buttonsDiv.style.justifyContent = 'center';
        buttonsDiv.style.gap = '10px';
        splitDownloadContainer.appendChild(buttonsDiv);

        // Create download buttons for each part
        parts.forEach((part, index) => {
            const partNumber = index + 1;
            const moviesCount = part.movies_info.length;
            const button = document.createElement('button');
            button.className = 'split-download-btn';
            button.textContent = `تحميل الجزء ${partNumber} (${moviesCount} فيلم)`;

            button.addEventListener('click', () => {
                // Create filename with base name, part number, and movies count
                const filename = `${baseName}_PART${partNumber}_of_${parts.length}_movies_${moviesCount}.json`;

                // Download the part
                downloadJsonData(part, filename);
            });

            buttonsDiv.appendChild(button);
        });

        // Show the download container
        splitDownloadContainer.style.display = 'block';
    }

    // Function to download JSON data
    function downloadJsonData(data, filename) {
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();

        // Clean up
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 100);
    }

    // Function to read file content
    function readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (event) => {
                resolve(event.target.result);
            };

            reader.onerror = (error) => {
                reject(error);
            };

            reader.readAsText(file);
        });
    }

    // Function to download the combined JSON
    function downloadCombinedJson() {
        if (!combinedData) {
            showError('لا توجد بيانات للتحميل.');
            return;
        }

        // Extract main website name from movies_href
        let mainWebsite = 'movies';
        const totalMovies = combinedData.movies_info.length;

        // Try to extract domain from the first movie's URL
        if (totalMovies > 0 && combinedData.movies_info[0].movies_href) {
            try {
                const url = new URL(combinedData.movies_info[0].movies_href);
                mainWebsite = url.hostname.replace('www.', '');

                // If hostname has multiple parts, take the domain name part
                const parts = mainWebsite.split('.');
                if (parts.length >= 2) {
                    mainWebsite = parts[parts.length - 2];
                }
            } catch (e) {
                // If URL parsing fails, try to extract domain using regex
                const match = combinedData.movies_info[0].movies_href.match(/\/\/(?:www\.)?([^\/]+)/i);
                if (match && match[1]) {
                    mainWebsite = match[1].split('.')[0];
                }
            }
        }

        // Create filename with website name, COMBINED keyword, and total movies count
        const filename = `${mainWebsite}_COMBINED_movies_${totalMovies}.json`;

        // Download the combined data
        downloadJsonData(combinedData, filename);
    }

    // Function to analyze JSON file
    async function analyzeJsonFile() {
        const file = analyzeFileInput.files[0];

        // Validate input
        if (!file) {
            showError('الرجاء اختيار ملف JSON للتحليل.');
            return;
        }

        try {
            // Reset UI
            previewElement.textContent = '';
            statsElement.textContent = '';
            splitDownloadContainer.style.display = 'none';
            splitDownloadContainer.innerHTML = '';

            // Read and parse the file
            const fileContent = await readFile(file);
            analyzedData = JSON.parse(fileContent);

            // Detect structure: new (categories/movies) or old (movies_info)
            let totalMovies = 0;
            let statsHtml = `<p>تم تحليل الملف <strong>${file.name}</strong> بنجاح.</p>`;
            let previewData = {};

            if (analyzedData.movies && Array.isArray(analyzedData.movies) && analyzedData.categories && Array.isArray(analyzedData.categories)) {
                // New structure with categories and movies
                totalMovies = analyzedData.movies.length;
                statsHtml += `<p>إجمالي الأفلام: <strong>${totalMovies}</strong></p>`;

                // Count movies per category
                let categoryStats = '<ul style="margin-right:20px">';
                analyzedData.categories.forEach(cat => {
                    // Count movies in this category (by subCategories)
                    const count = analyzedData.movies.filter(mov => Array.isArray(mov.subCategories) && mov.subCategories.includes(cat.id)).length;
                    categoryStats += `<li><strong>${cat.name}</strong> (<span style='color:blue'>${count}</span>)</li>`;
                });
                categoryStats += '</ul>';
                statsHtml += `<p>عدد الأفلام في كل قسم:</p>${categoryStats}`;

                previewData = {
                    categories: analyzedData.categories,
                    movies: analyzedData.movies.slice(0, 5)
                };
                if (analyzedData.movies.length > 5) {
                    previewData._note = `عرض 5 من أصل ${analyzedData.movies.length} فيلم`;
                }
            } else if (analyzedData.movies_info && Array.isArray(analyzedData.movies_info)) {
                // Old structure
                totalMovies = analyzedData.movies_info.length;
                statsHtml += `<p>إجمالي الأفلام: <strong>${totalMovies}</strong></p>`;
                previewData = {
                    movies_info: analyzedData.movies_info.slice(0, 5)
                };
                if (analyzedData.movies_info.length > 5) {
                    previewData._note = `عرض 5 من أصل ${analyzedData.movies_info.length} فيلم`;
                }
            } else {
                showError(`الملف ${file.name} لا يحتوي على بنية صحيحة.`);
                return;
            }

            statsElement.innerHTML = statsHtml;
            previewElement.textContent = JSON.stringify(previewData, null, 2);

            // Create download button
            createAnalyzeDownloadButton(file.name, analyzedData);

        } catch (error) {
            showError(`حدث خطأ: ${error.message}`);
        }
    }

    // Function to create download button for analyzed file
    function createAnalyzeDownloadButton(originalFileName, data) {
        splitDownloadContainer.innerHTML = '';

        // Extract base name from original file
        let baseName = originalFileName.replace('.json', '');

        // Try to extract domain from the first movie's URL if available
        if (data.movies_info.length > 0 && data.movies_info[0].movies_href) {
            try {
                const url = new URL(data.movies_info[0].movies_href);
                let domain = url.hostname.replace('www.', '');

                // If hostname has multiple parts, take the domain name part
                const domainParts = domain.split('.');
                if (domainParts.length >= 2) {
                    baseName = domainParts[domainParts.length - 2];
                } else {
                    baseName = domain;
                }
            } catch (e) {
                // If URL parsing fails, try to extract domain using regex
                const match = data.movies_info[0].movies_href.match(/\/\/(?:www\.)?([^\/]+)/i);
                if (match && match[1]) {
                    baseName = match[1].split('.')[0];
                }
            }
        }

        // Create container title
        const containerTitle = document.createElement('h3');
        containerTitle.textContent = 'تحميل الملف:';
        containerTitle.style.marginBottom = '10px';
        containerTitle.style.textAlign = 'center';
        splitDownloadContainer.appendChild(containerTitle);

        // Create button
        const button = document.createElement('button');
        button.className = 'split-download-btn';
        const moviesCount = data.movies_info.length;
        button.textContent = `تحميل الملف (${moviesCount} فيلم)`;

        button.addEventListener('click', () => {
            // Create filename with base name and movies count
            const filename = `${baseName}_movies_${moviesCount}.json`;

            // Download the data
            downloadJsonData(data, filename);
        });

        splitDownloadContainer.appendChild(button);

        // Show the download container
        splitDownloadContainer.style.display = 'block';
    }

    // Function to display folder structure
    function displayFolderStructure(structure, currentPath = '') {
        cardsContainer.innerHTML = '';

        // Create breadcrumb for navigation
        const breadcrumb = document.createElement('div');
        breadcrumb.className = 'folder-breadcrumb';

        // Build breadcrumb path
        let breadcrumbHTML = `<span class="breadcrumb-item" data-path="">الرئيسية</span>`;
        if (currentPath) {
            const pathParts = currentPath.split('/');
            let buildPath = '';
            pathParts.forEach((part, index) => {
                buildPath += (index > 0 ? '/' : '') + part;
                breadcrumbHTML += ` > <span class="breadcrumb-item" data-path="${buildPath}">${part}</span>`;
            });
        }

        breadcrumb.innerHTML = breadcrumbHTML;
        cardsContainer.appendChild(breadcrumb);

        // Create folders container
        const foldersContainer = document.createElement('div');
        foldersContainer.className = 'folders-container';
        cardsContainer.appendChild(foldersContainer);

        // Get folders and files for current path
        const { folders, files } = getFoldersAndFiles(structure, currentPath);

        // Display folders first
        folders.forEach(folderName => {
            const folder = document.createElement('div');
            folder.className = 'folder-item';

            const fullPath = currentPath ? `${currentPath}/${folderName}` : folderName;
            const subItems = getSubItemsCount(structure, fullPath);

            folder.innerHTML = `
                <div class="folder-icon">📁</div>
                <div class="folder-info">
                    <div class="folder-name">${folderName}</div>
                    <div class="folder-count">${subItems.folders} مجلد، ${subItems.files} ملف JSON</div>
                </div>
            `;

            folder.addEventListener('click', () => displayFolderStructure(structure, fullPath));
            foldersContainer.appendChild(folder);
        });

        // Display JSON files
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';

            fileItem.innerHTML = `
                <div class="file-icon">📄</div>
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">ملف JSON</div>
                </div>
            `;

            fileItem.addEventListener('click', () => processAndDisplayMovies([file], currentPath));
            foldersContainer.appendChild(fileItem);
        });

        // If no folders or files, show empty message
        if (folders.length === 0 && files.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-folder-message';
            emptyMessage.textContent = 'هذا المجلد فارغ';
            foldersContainer.appendChild(emptyMessage);
        }

        // Update movies count
        const totalFiles = Object.values(structure).reduce((sum, files) => sum + files.length, 0);
        updateMoviesCount(0, 0, totalFiles);
    }

    // Helper function to get folders and files for current path
    function getFoldersAndFiles(structure, currentPath) {
        const folders = new Set();
        const files = [];

        Object.keys(structure).forEach(folderPath => {
            if (currentPath === '') {
                // Root level - show immediate subfolders and root files
                if (folderPath === 'root') {
                    files.push(...structure[folderPath]);
                } else {
                    const firstLevel = folderPath.split('/')[0];
                    folders.add(firstLevel);
                }
            } else {
                // Inside a folder - show direct subfolders and files
                if (folderPath === currentPath) {
                    // Files in current folder
                    files.push(...structure[folderPath]);
                } else if (folderPath.startsWith(currentPath + '/')) {
                    const relativePath = folderPath.substring(currentPath.length + 1);
                    const parts = relativePath.split('/');
                    if (parts.length >= 1) {
                        // Direct subfolder
                        folders.add(parts[0]);
                    }
                }
            }
        });

        return { folders: Array.from(folders).sort(), files };
    }

    // Helper function to count subfolders and files
    function getSubItemsCount(structure, path) {
        const subFolders = new Set();
        let files = 0;

        Object.keys(structure).forEach(folderPath => {
            if (folderPath === path) {
                // Files directly in this folder
                files += structure[folderPath].length;
            } else if (folderPath.startsWith(path + '/')) {
                // Subfolders
                const relativePath = folderPath.substring(path.length + 1);
                const parts = relativePath.split('/');
                if (parts.length >= 1) {
                    subFolders.add(parts[0]);
                }
            }
        });

        return { folders: subFolders.size, files };
    }

    // Modified function to handle file processing
    async function processJsonFiles(files, currentFolder = '') {
        const progressContainer = document.getElementById('progress-container');
        const progressText = document.getElementById('progress-text');
        const progressPercentage = document.getElementById('progress-percentage');
        const progressFill = document.getElementById('progress-fill');
        const progressDetails = document.getElementById('progress-details');
        
        progressContainer.style.display = 'block';
        progressText.textContent = 'جاري معالجة الملفات...';
        progressPercentage.textContent = '0%';
        progressFill.style.width = '0%';
        progressDetails.textContent = '';

        try {
            // Process all files
            let allMoviesArr = [];
            let processedFiles = 0;

            for (const currentFile of files) {
                try {
                    const fileContent = await readFile(currentFile);
                    let json = JSON.parse(fileContent);

                    // Update progress
                    processedFiles++;
                    const progress = (processedFiles / files.length) * 50;
                    progressFill.style.width = progress + '%';
                    progressPercentage.textContent = Math.round(progress) + '%';
                    progressDetails.textContent = `تمت معالجة ${processedFiles} من ${files.length} ملف`;

                    // Extract movies from the current file
                    let fileMovies = [];
                    if (json.movies_info && Array.isArray(json.movies_info)) {
                        fileMovies = json.movies_info;
                    } else if (json.movies && Array.isArray(json.movies)) {
                        fileMovies = json.movies;
                    } else if (typeof json === 'object') {
                        fileMovies = extractMoviesFromObject(json);
                    }

                    // Add source folder information to each movie
                    fileMovies = fileMovies.map(movie => ({
                        ...movie,
                        sourceFolder: currentFolder || 'root',
                        sourceFile: currentFile.name
                    }));

                    allMoviesArr = allMoviesArr.concat(fileMovies);
                } catch (e) {
                    showError(`خطأ في معالجة الملف ${currentFile.name}: ${e.message}`);
                }
            }

            // Process movies and display them
            displayProcessedMovies(allMoviesArr, currentFolder);

        } catch (error) {
            progressContainer.style.display = 'none';
            showError(`حدث خطأ: ${error.message}`);
        }
    }

    // Function to extract movies from nested object structure
    function extractMoviesFromObject(obj) {
        let arr = [];
        for (const key in obj) {
            if (Array.isArray(obj[key])) {
                obj[key].forEach(item => {
                    if (typeof item === 'object') {
                        if (item.movies_name || item.series_name || item.title || item.name) {
                            arr.push(item);
                        } else {
                            arr = arr.concat(extractMoviesFromObject(item));
                        }
                    }
                });
            } else if (typeof obj[key] === 'object') {
                arr = arr.concat(extractMoviesFromObject(obj[key]));
            }
        }
        return arr;
    }

    // Function to display processed movies
    async function displayProcessedMovies(moviesArr, currentFolder) {
        // Process movies
        const processedMovies = moviesArr.map(movie => ({
            name: movie.movies_name || movie.series_name || movie.title || movie.name || '',
            img: movie.movies_img || movie.series_img || movie.imageUrl || movie.img || '',
            href: movie.movies_href || movie.series_href || movie.link || movie.href || '',
            category: movie.category || movie.section || '',
            sourceFolder: movie.sourceFolder,
            sourceFile: movie.sourceFile
        })).filter(m => m.name && m.href);

        // Clear container and add back button
        cardsContainer.innerHTML = '';
        
        // Add breadcrumb navigation
        const breadcrumb = document.createElement('div');
        breadcrumb.className = 'folder-breadcrumb';
        const pathParts = currentFolder.split('/');
        let currentPath = '';
        
        breadcrumb.innerHTML = `<span class="breadcrumb-item" data-path="">الرئيسية</span>`;
        pathParts.forEach((part, index) => {
            if (part && part !== 'root') {
                currentPath += (currentPath ? '/' : '') + part;
                breadcrumb.innerHTML += ` / <span class="breadcrumb-item" data-path="${currentPath}">${part}</span>`;
            }
        });
        
        cardsContainer.appendChild(breadcrumb);

        // Add cards container
        const movieCardsContainer = document.createElement('div');
        movieCardsContainer.className = 'movie-cards-grid';
        cardsContainer.appendChild(movieCardsContainer);

        // Display movies in batches
        const batch = 40;
        let processed = 0;
        
        function renderBatch(start) {
            for (let i = start; i < Math.min(start + batch, processedMovies.length); i++) {
                const card = createMovieCard(processedMovies[i], i);
                movieCardsContainer.appendChild(card);
                processed++;
            }
            if (processed < processedMovies.length) {
                setTimeout(() => renderBatch(processed), 0);
            }
        }

        renderBatch(0);
        updateMoviesCount(processedMovies.length, processedMovies.length);
        
        // Show controls and update UI
        cardsControls.style.display = 'block';
        downloadUpdatedBtn.style.display = 'none';
        document.querySelector('.container').classList.add('full-width');
    }

    // Function to load and display movie cards
    async function loadMovieCards() {
        const file = cardsFileInput.files[0];
        const folderFiles = cardsFolderInput.files;

        if (!file && (!folderFiles || folderFiles.length === 0)) {
            showError('الرجاء اختيار ملف JSON أو مجلد يحتوي على ملفات JSON.');
            return;
        }

        // Clear the other input when one is selected
        if (file) {
            cardsFolderInput.value = '';
        } else if (folderFiles.length > 0) {
            cardsFileInput.value = '';
        }

        // If it's a single file, process it normally
        if (file) {
            let filesToProcess = [file];
            processAndDisplayMovies(filesToProcess);
            return;
        }

        // For folder selection, organize files by their directory structure
        let folderStructure = {};
        Array.from(folderFiles).forEach(file => {
            if (!file.name.toLowerCase().endsWith('.json')) return;

            // Get the relative path from the selected folder
            const path = file.webkitRelativePath;
            const parts = path.split('/');

            // Skip the first part as it's the root folder name
            parts.shift();

            // If the file is directly in the root, put it in a "root" folder
            if (parts.length === 1) {
                if (!folderStructure['root']) {
                    folderStructure['root'] = [];
                }
                folderStructure['root'].push(file);
            } else {
                // Store file in its exact folder path
                const folder = parts.slice(0, -1).join('/');
                if (!folderStructure[folder]) {
                    folderStructure[folder] = [];
                }
                folderStructure[folder].push(file);
            }
        });

        // Store folder structure for navigation and display it
        window._lastFolderStructure = folderStructure;
        displayFolderStructure(folderStructure);
    }

    // Function to load and display movie cards from a single file or folder
    async function processAndDisplayMovies(filesToProcess) {
        try {
            // Show progress indicator
            const progressContainer = document.getElementById('progress-container');
            const progressText = document.getElementById('progress-text');
            const progressPercentage = document.getElementById('progress-percentage');
            const progressFill = document.getElementById('progress-fill');
            const progressDetails = document.getElementById('progress-details');
            progressContainer.style.display = 'block';
            progressText.textContent = 'جاري معالجة الملفات...';
            progressPercentage.textContent = '0%';
            progressFill.style.width = '0%';
            progressDetails.textContent = '';

            // Read and parse all files
            let allMoviesArr = [];
            let processedFiles = 0;

            for (const currentFile of filesToProcess) {
                try {
                    const fileContent = await readFile(currentFile);
                    let json = JSON.parse(fileContent);

                    // Update progress
                    processedFiles++;
                    const progress = (processedFiles / filesToProcess.length) * 50; // Use first 50% for file processing
                    progressFill.style.width = progress + '%';
                    progressPercentage.textContent = Math.round(progress) + '%';
                    progressDetails.textContent = `تمت معالجة ${processedFiles} من ${filesToProcess.length} ملف`;

                    // Extract movies from the current file
                    let fileMovies = [];
                    if (json.movies_info && Array.isArray(json.movies_info)) {
                        fileMovies = json.movies_info;
                    } else if (json.movies && Array.isArray(json.movies)) {
                        fileMovies = json.movies;
                    } else if (typeof json === 'object') {
                        fileMovies = extractMoviesFromObject(json);
                    }

                    allMoviesArr = allMoviesArr.concat(fileMovies);

                } catch (e) {
                    showError(`خطأ في معالجة الملف ${currentFile.name}: ${e.message}`);
                }
            }

            // Process all movies and extract card fields
            const moviesArr = allMoviesArr.map(movie => ({
                name: movie.movies_name || movie.series_name || movie.title || movie.name || '',
                img: movie.movies_img || movie.series_img || movie.imageUrl || movie.img || '',
                href: movie.movies_href || movie.series_href || movie.link || movie.href || '',
                category: movie.category || movie.section || ''
            })).filter(m => m.name && m.href);

            // Update progress for movie processing
            progressText.textContent = 'جاري معالجة الأفلام...';
            progressFill.style.width = '75%';
            progressPercentage.textContent = '75%';
            progressDetails.textContent = `تم العثور على ${moviesArr.length} فيلم`;

            // Group movies by category
            const categoryMap = {};
            moviesArr.forEach(m => {
                let cat = m.category ? String(m.category).trim() : 'غير مصنف';
                if (!categoryMap[cat]) categoryMap[cat] = [];
                categoryMap[cat].push(m);
            });
            const categories = Object.keys(categoryMap);

            // Update progress before displaying movies
            progressText.textContent = 'جاري تنظيم الأفلام...';
            progressFill.style.width = '90%';
            progressPercentage.textContent = '90%';

            // If only one category, display cards directly
            if (categories.length === 1) {
                displayMovieCards(categoryMap[categories[0]], categories[0]);
            } else {
                // Display categories as folders
                displayCategoryFolders(categoryMap);
            }

            // Finalize progress
            progressText.textContent = 'اكتمل التحميل.';
            progressFill.style.width = '100%';
            progressPercentage.textContent = '100%';
            setTimeout(() => { progressContainer.style.display = 'none'; }, 700);

            // Show controls and update UI
            cardsControls.style.display = 'block';
            downloadUpdatedBtn.style.display = 'none';
            document.querySelector('.container').classList.add('full-width');

        } catch (error) {
            document.getElementById('progress-container').style.display = 'none';
            showError(`حدث خطأ: ${error.message}`);
        }
    }

    // Function to display category folders
    function displayCategoryFolders(categoryMap) {
        cardsContainer.innerHTML = '';
        allMovies = [];
        window._categoryMoviesCache = categoryMap; // Cache movies by category

        Object.keys(categoryMap).forEach(cat => {
            const folder = document.createElement('div');
            folder.className = 'category-folder';
            folder.style.cssText = 'display:inline-block; margin:10px; padding:20px 30px; background:#f1f1f1; border-radius:8px; cursor:pointer; font-weight:bold; font-size:18px; box-shadow:0 2px 8px #eee; transition:background 0.2s;';
            folder.textContent = `${cat} (${categoryMap[cat].length})`;
            folder.onclick = () => displayMovieCards(categoryMap[cat], cat);
            folder.onmouseover = () => { folder.style.background = '#e0ffe0'; };
            folder.onmouseout = () => { folder.style.background = '#f1f1f1'; };
            cardsContainer.appendChild(folder);
        });

        updateMoviesCount(0, 0);
    }

    // Function to display movie cards for a specific category
    function displayMovieCards(movies, categoryName) {
        cardsContainer.innerHTML = '';
        
        // Add back button to return to category folders
        const backBtn = document.createElement('button');
        backBtn.textContent = '◀ عودة للأقسام';
        backBtn.style.cssText = 'margin-bottom:20px; background:#007bff; color:#fff; font-size:16px; padding:8px 18px; border-radius:6px;';
        backBtn.onclick = () => displayCategoryFolders(window._categoryMoviesCache);
        cardsContainer.appendChild(backBtn);

        // Add a container for the cards grid
        const movieCardsGrid = document.createElement('div');
        movieCardsGrid.className = 'movie-cards-grid';
        cardsContainer.appendChild(movieCardsGrid);

        // Store current movies for editing/deleting
        window._currentDisplayedMovies = movies;
        window._currentCategory = categoryName;

        // Render cards in batches
        let processed = 0;
        const total = movies.length;
        const batch = 40;
        
        function renderBatch(start) {
            for (let i = start; i < Math.min(start + batch, total); i++) {
                const card = createMovieCard(movies[i], i);
                movieCardsGrid.appendChild(card);
                processed++;
            }
            if (processed < total) {
                setTimeout(() => renderBatch(processed), 0);
            }
        }
        
        renderBatch(0);
        updateMoviesCount(total, total);
    }

    // عرض بطاقات قسم معين حسب category
    function showCategoryCardsByCategory(cat, catName) {
        cardsContainer.innerHTML = '';
        const backBtn = document.createElement('button');
        backBtn.textContent = '◀ عودة للأقسام';
        backBtn.style.cssText = 'margin-bottom:20px; background:#007bff; color:#fff; font-size:16px; padding:8px 18px; border-radius:6px;';
        backBtn.onclick = () => loadMovieCards();
        cardsContainer.appendChild(backBtn);
        let moviesArr = (window._categoryMoviesCache && window._categoryMoviesCache[cat]) ? window._categoryMoviesCache[cat] : [];
        // إعادة استخراج الحقول المطلوبة من كل عنصر
        moviesArr = moviesArr.map(obj => ({
            name: obj.movies_name || obj.series_name || obj.title || obj.name || '',
            img: obj.movies_img || obj.series_img || obj.imageUrl || obj.img || '',
            href: obj.movies_href || obj.series_href || obj.link || obj.href || '',
            category: obj.category || obj.section || cat
        })).filter(m => m.name && m.href);
        if (!moviesArr || moviesArr.length === 0) {
            const msg = document.createElement('div');
            msg.textContent = `لا توجد أفلام في القسم: ${catName}`;
            msg.style.cssText = 'text-align:center; color:#666; margin-top:30px; font-size:18px;';
            cardsContainer.appendChild(msg);
            updateMoviesCount(0, 0);
            return;
        }
        // حفظ مصفوفة البطاقات المعروضة حالياً في متغير عام
        window._currentDisplayedMovies = moviesArr;
        window._currentCategory = cat;
        // عرض البطاقات بدفعات سريعة
        let processed = 0;
        const total = moviesArr.length;
        const batch = 40;
        function renderBatch(start) {
            for (let i = start; i < Math.min(start + batch, total); i++) {
                const card = createMovieCard(moviesArr[i], i);
                cardsContainer.appendChild(card);
                processed++;
            }
            if (processed < total) {
                setTimeout(() => renderBatch(processed), 0);
            }
        }
        renderBatch(0);
        updateMoviesCount(moviesArr.length, moviesArr.length);
    }

    // Function to create a movie card
    function createMovieCard(movie, index) {
        const card = document.createElement('div');
        card.className = 'movie-card';

        card.innerHTML = `
            <div class="movie-number">${index + 1}</div>
            <img class="movie-poster" src="${movie.img}" alt="${movie.name}"
                 onerror="this.className='movie-poster error'; this.innerHTML='صورة غير متاحة';">
            <div class="movie-info">
                <h3 class="movie-title">${movie.name}</h3>
                <div class="movie-actions">
                    <a href="${movie.href}" target="_blank" class="action-btn play-btn">
                        ▶ تشغيل
                    </a>
                    <button class="action-btn edit-btn" onclick="editMovie(${index})">
                        ✏ تعديل
                    </button>
                    <button class="action-btn delete-btn" onclick="deleteMovie(${index})">
                        🗑 حذف
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    // Function to edit movie (تعمل على البطاقات المعروضة حالياً)
    window.editMovie = function(index) {
        if (!window._currentDisplayedMovies || !window._currentDisplayedMovies[index]) return;
        const movie = window._currentDisplayedMovies[index];
        window._currentEditIndex = index;
        // Fill the form with current data
        editMovieName.value = movie.name;
        editMovieImg.value = movie.img;
        editMovieHref.value = movie.href;
        // Show the modal
        editModal.style.display = 'block';
    };

    // Function to delete movie (تعمل على البطاقات المعروضة حالياً)
    window.deleteMovie = function(index) {
        if (!window._currentDisplayedMovies || !window._currentDisplayedMovies[index]) return;
        const movie = window._currentDisplayedMovies[index];
        if (confirm(`هل أنت متأكد من حذف الفيلم "${movie.name}"؟`)) {
            // Remove the movie from the array
            window._currentDisplayedMovies.splice(index, 1);
            // Re-display the cards for the current category
            displayMovieCards(window._currentDisplayedMovies, window._currentCategory);
            showSuccess(`تم حذف الفيلم "${movie.name}" بنجاح.`);
        }
    };

    // Function to close edit modal
    function closeEditModal() {
        editModal.style.display = 'none';
        window._currentEditIndex = -1;
        editForm.reset();
    }

    // Function to save movie edit (تعمل على البطاقات المعروضة حالياً)
    function saveMovieEdit(event) {
        event.preventDefault();
        if (window._currentEditIndex === -1 || !window._currentDisplayedMovies || !window._currentDisplayedMovies[window._currentEditIndex]) {
            showError('خطأ في تحديد الفيلم للتعديل.');
            return;
        }
        // Update the movie data
        const updatedMovie = {
            name: editMovieName.value.trim(),
            img: editMovieImg.value.trim(),
            href: editMovieHref.value.trim(),
            category: window._currentDisplayedMovies[window._currentEditIndex].category || window._currentCategory
        };
        window._currentDisplayedMovies[window._currentEditIndex] = updatedMovie;
        
        // Re-display the cards for the current category
        displayMovieCards(window._currentDisplayedMovies, window._currentCategory);
        closeEditModal();
        showSuccess('تم تحديث بيانات الفيلم بنجاح.');
    }

    // Function to show success message
    function showSuccess(message) {
        const successElement = document.createElement('div');
        successElement.className = 'success';
        successElement.style.cssText = `
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            border: 1px solid #c3e6cb;
        `;
        successElement.textContent = message;

        // Remove any existing success messages
        const existingSuccess = cardsTab.querySelectorAll('.success');
        existingSuccess.forEach(el => el.remove());

        // Add new success message
        cardsTab.insertBefore(successElement, cardsContainer);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            successElement.remove();
        }, 3000);
    }

    // Function to download updated data
    function downloadUpdatedData() {
        if (!cardsData || !cardsData.movies_info) {
            showError('لا توجد بيانات للتحميل.');
            return;
        }

        // Create filename with timestamp
        const now = new Date();
        const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '');
        const filename = `movies_updated_${timestamp}.json`;

        // Download the updated data
        downloadJsonData(cardsData, filename);

        showSuccess('تم تحميل البيانات المحدثة بنجاح.');
    }

    // Function to filter movies based on search term
    function filterMovies(searchTerm) {
        if (!allMovies || allMovies.length === 0) return;

        let filteredMovies;

        if (searchTerm === '') {
            // Show all movies if search is empty
            filteredMovies = allMovies;
        } else {
            // Filter movies based on search term
            filteredMovies = allMovies.filter(movie =>
                movie.movies_name.toLowerCase().includes(searchTerm)
            );
        }

        // Display filtered movies
        displayMovieCards(filteredMovies);

        // Update movies count
        updateMoviesCount(filteredMovies.length, allMovies.length);
    }

    // Function to update movies count display
    function updateMoviesCount(displayed, total = null) {
        if (total === null) {
            total = displayed;
        }

        if (displayed === total) {
            moviesCount.textContent = `إجمالي الأفلام: ${total}`;
        } else {
            moviesCount.textContent = `عرض ${displayed} من أصل ${total} فيلم`;
        }
    }

    // Function to clean JSON file by removing specific movie
    function cleanJsonFile() {
        const file = cleanFileInput.files[0];
        const movieNameToRemove = movieNameInput.value.trim();

        if (!file) {
            alert('يرجى اختيار ملف JSON للتنظيف');
            return;
        }

        if (!movieNameToRemove) {
            alert('يرجى إدخال اسم الفيلم المراد حذفه');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const jsonData = JSON.parse(e.target.result);

                // Check if the JSON has the expected structure
                if (!jsonData.movies_info || !Array.isArray(jsonData.movies_info)) {
                    alert('تنسيق الملف غير صحيح. يجب أن يحتوي على مصفوفة movies_info');
                    return;
                }

                const originalCount = jsonData.movies_info.length;
                const removedMovies = [];

                // Filter out movies with the specified name
                jsonData.movies_info = jsonData.movies_info.filter(movie => {
                    if (movie.movies_name && movie.movies_name.toLowerCase().includes(movieNameToRemove.toLowerCase())) {
                        removedMovies.push(movie);
                        return false; // Remove this movie
                    }
                    return true; // Keep this movie
                });

                const newCount = jsonData.movies_info.length;
                const removedCount = originalCount - newCount;

                if (removedCount === 0) {
                    alert(`لم يتم العثور على أي فيلم يحتوي على "${movieNameToRemove}"`);
                    return;
                }

                // Store cleaned data
                cleanedData = jsonData;

                // Show preview
                showCleanPreview(originalCount, newCount, removedCount, removedMovies);

                // Show download button in result section
                downloadBtn.disabled = false;
                downloadBtn.textContent = 'تحميل الملف المنظف';
                downloadBtn.onclick = downloadCleanedJson;
                resultSection.style.display = 'block';

                // Update preview
                previewElement.textContent = JSON.stringify(jsonData, null, 2);
                statsElement.innerHTML = `
                    <strong>إحصائيات التنظيف:</strong><br>
                    العدد الأصلي: ${originalCount} فيلم<br>
                    العدد بعد التنظيف: ${newCount} فيلم<br>
                    تم حذف: ${removedCount} فيلم
                `;

            } catch (error) {
                alert('خطأ في قراءة الملف: ' + error.message);
            }
        };

        reader.readAsText(file);
    }

    // Function to show clean preview
    function showCleanPreview(originalCount, newCount, removedCount, removedMovies) {
        cleanStats.innerHTML = `
            <strong>إحصائيات التنظيف:</strong><br>
            العدد الأصلي: ${originalCount} فيلم<br>
            العدد بعد التنظيف: ${newCount} فيلم<br>
            تم حذف: ${removedCount} فيلم
        `;

        // Show removed movies
        if (removedMovies.length > 0) {
            let removedHtml = '<h4>الأفلام المحذوفة:</h4>';
            removedMovies.forEach(movie => {
                removedHtml += `
                    <div class="removed-movie-item">
                        <h4>${movie.movies_name || 'بدون اسم'}</h4>
                        <p><strong>رابط الصورة:</strong> ${movie.movies_img || 'غير متوفر'}</p>
                        <p><strong>رابط الفيلم:</strong> ${movie.movies_href || 'غير متوفر'}</p>
                    </div>
                `;
            });
            removedMovies.innerHTML = removedHtml;
        }

        cleanPreview.style.display = 'block';
    }

    // Function to download cleaned JSON
    function downloadCleanedJson() {
        if (!cleanedData) {
            alert('لا توجد بيانات منظفة للتحميل');
            return;
        }

        const jsonString = JSON.stringify(cleanedData, null, 2);
        const originalFileName = cleanFileInput.files[0].name;
        const cleanedFileName = originalFileName.replace('.json', '_cleaned.json');

        downloadFile(jsonString, cleanedFileName);
    }

    // Function to remove duplicate movies
    function removeDuplicateMovies() {
        const file = cleanFileInput.files[0];

        if (!file) {
            alert('يرجى اختيار ملف JSON لإزالة الأفلام المكررة');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const jsonData = JSON.parse(e.target.result);

                // Check if the JSON has the expected structure
                if (!jsonData.movies_info || !Array.isArray(jsonData.movies_info)) {
                    alert('تنسيق الملف غير صحيح. يجب أن يحتوي على مصفوفة movies_info');
                    return;
                }

                const originalCount = jsonData.movies_info.length;
                const duplicateMovies = [];
                const uniqueMovies = [];
                const seenMovies = new Set();

                // Find duplicates based on movie name
                jsonData.movies_info.forEach(movie => {
                    const movieName = movie.movies_name ? movie.movies_name.trim().toLowerCase() : '';

                    if (movieName && seenMovies.has(movieName)) {
                        // This is a duplicate
                        duplicateMovies.push(movie);
                    } else {
                        // This is unique
                        if (movieName) {
                            seenMovies.add(movieName);
                        }
                        uniqueMovies.push(movie);
                    }
                });

                const newCount = uniqueMovies.length;
                const removedCount = duplicateMovies.length;

                if (removedCount === 0) {
                    alert('لم يتم العثور على أي أفلام مكررة في الملف');
                    return;
                }

                // Update the JSON data
                jsonData.movies_info = uniqueMovies;

                // Store cleaned data
                cleanedData = jsonData;

                // Show preview
                showDuplicateRemovalPreview(originalCount, newCount, removedCount, duplicateMovies);

                // Show download button in result section
                downloadBtn.disabled = false;
                downloadBtn.textContent = 'تحميل الملف بدون تكرار';
                downloadBtn.onclick = downloadCleanedJson;
                resultSection.style.display = 'block';

                // Update preview
                previewElement.textContent = JSON.stringify(jsonData, null, 2);
                statsElement.innerHTML = `
                    <strong>إحصائيات إزالة التكرار:</strong><br>
                    العدد الأصلي: ${originalCount} فيلم<br>
                    العدد بعد إزالة التكرار: ${newCount} فيلم<br>
                    تم حذف: ${removedCount} فيلم مكرر
                `;

            } catch (error) {
                alert('خطأ في قراءة الملف: ' + error.message);
            }
        };

        reader.readAsText(file);
    }

    // Function to show duplicate removal preview
    function showDuplicateRemovalPreview(originalCount, newCount, removedCount, duplicateMovies) {
        cleanStats.innerHTML = `
            <strong>إحصائيات إزالة التكرار:</strong><br>
            العدد الأصلي: ${originalCount} فيلم<br>
            العدد بعد إزالة التكرار: ${newCount} فيلم<br>
            تم حذف: ${removedCount} فيلم مكرر
        `;

        // Show removed duplicate movies
        if (duplicateMovies.length > 0) {
            let removedHtml = '<h4>الأفلام المكررة المحذوفة:</h4>';
            duplicateMovies.forEach(movie => {
                removedHtml += `
                    <div class="removed-movie-item">
                        <h4>${movie.movies_name || 'بدون اسم'}</h4>
                        <p><strong>رابط الصورة:</strong> ${movie.movies_img || 'غير متوفر'}</p>
                        <p><strong>رابط الفيلم:</strong> ${movie.movies_href || 'غير متوفر'}</p>
                    </div>
                `;
            });
            removedMovies.innerHTML = removedHtml;
        }

        cleanPreview.style.display = 'block';
    }

    // Function to show error message
    function showError(message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'error';
        errorElement.textContent = message;

        // Remove any existing error messages
        const existingErrors = resultSection.querySelectorAll('.error');
        existingErrors.forEach(el => el.remove());

        // Add new error message
        resultSection.insertBefore(errorElement, resultSection.firstChild);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            errorElement.remove();
        }, 5000);
    }

    // Function to download file
    function downloadFile(data, filename, type = 'application/json') {
        const blob = new Blob([data], { type });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Add event delegation for breadcrumb navigation
    cardsContainer.addEventListener('click', function(e) {
        const breadcrumbItem = e.target.closest('.breadcrumb-item');
        if (!breadcrumbItem) return;

        const path = breadcrumbItem.dataset.path;
        // Navigate to the selected folder level
        displayFolderStructure(window._lastFolderStructure || {}, path);
    });
});
