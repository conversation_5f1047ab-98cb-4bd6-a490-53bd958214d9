# تعليمات استخدام المجلدات المتداخلة

## الميزة الجديدة
تم إضافة دعم كامل للمجلدات المتداخلة في تطبيق عرض بطاقات الأفلام. يمكنك الآن:

1. **تحميل مجلد رئيسي** يحتوي على مجلدات فرعية
2. **التنقل بين المجلدات** بطريقة هرمية منظمة
3. **عرض ملفات JSON منفردة** قابلة للنقر
4. **استخدام شريط التنقل** للعودة للمستويات السابقة

## كيفية الاختبار

### الخطوة 1: فتح التطبيق
1. افتح ملف `index.html` في المتصفح
2. انتقل إلى تبويب "عرض بطاقات الأفلام"

### الخطوة 2: تحميل المجلد التجريبي
1. انقر على "اختر مجلد يحتوي على ملفات JSON"
2. اختر مجلد `test-nested-folders` الموجود في نفس المجلد
3. انقر على "تحميل البطاقات"

### الخطوة 3: استكشاف البنية
ستشاهد:
- **مجلد movies** (2 مجلد، 0 ملف JSON)
- **مجلد series** (2 مجلد، 0 ملف JSON)  
- **ملف root-movies.json** (ملف JSON منفرد)

### الخطوة 4: التنقل في المجلدات
1. **انقر على مجلد "movies"** لرؤية:
   - مجلد action (0 مجلد، 1 ملف JSON)
   - مجلد comedy (0 مجلد، 1 ملف JSON)

2. **انقر على مجلد "action"** لرؤية:
   - ملف action-movies.json

3. **انقر على ملف "action-movies.json"** لعرض بطاقات الأفلام

### الخطوة 5: استخدام شريط التنقل
- انقر على "الرئيسية" للعودة للمستوى الأول
- انقر على "movies" للعودة لمجلد الأفلام
- انقر على "action" للعودة لمجلد الأكشن

## البنية التجريبية المتوفرة

```
test-nested-folders/
├── movies/
│   ├── action/
│   │   └── action-movies.json (3 أفلام أكشن)
│   └── comedy/
│       └── comedy-movies.json (2 فيلم كوميديا)
├── series/
│   ├── drama/
│   │   └── drama-series.json (2 مسلسل دراما)
│   └── thriller/
│       └── thriller-series.json (1 مسلسل إثارة)
└── root-movies.json (1 فيلم في المجلد الرئيسي)
```

## المميزات الجديدة

### 1. عرض هرمي
- المجلدات تظهر أولاً مع أيقونة 📁
- الملفات تظهر بعدها مع أيقونة 📄
- عدادات تظهر محتويات كل مجلد

### 2. تنقل سهل
- شريط تنقل (breadcrumb) يوضح المسار الحالي
- إمكانية النقر على أي مستوى للعودة إليه
- تصميم واضح ومنظم

### 3. عرض الملفات المنفردة
- كل ملف JSON يظهر كعنصر منفصل
- النقر على الملف يعرض محتواه كبطاقات
- لا حاجة لفتح جميع الملفات مرة واحدة

### 4. تصميم محسن
- ألوان مميزة للمجلدات والملفات
- تأثيرات hover جذابة
- تصميم متجاوب للهواتف المحمولة

## نصائح للاستخدام

1. **للمجلدات الكبيرة**: استخدم هذه الطريقة لتنظيم أفضل
2. **للملفات المنفردة**: لا تزال طريقة تحميل الملف الواحد متاحة
3. **للبحث السريع**: استخدم مربع البحث بعد تحميل الملفات
4. **للتنظيم**: رتب ملفاتك في مجلدات حسب النوع أو الفئة

## استكشاف الأخطاء

إذا لم تعمل الميزة:
1. تأكد من أن المتصفح يدعم `webkitdirectory`
2. تأكد من وجود ملفات JSON في المجلدات
3. تحقق من أن أسماء الملفات تنتهي بـ `.json`
4. جرب إعادة تحميل الصفحة
